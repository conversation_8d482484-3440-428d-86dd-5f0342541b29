/**
 * LEVEL-23 QUIZ IMPLEMENTATION - TECHDOS GAME
 *
 * OVERVIEW:
 * Level-23 represents the twenty-third stage of the TechDOS quiz game, featuring a comprehensive
 * jumbled sentence puzzle format designed to test language comprehension and unscrambling skills.
 * Players must identify the correct unscrambled version from multiple choice options.
 *
 * QUIZ MECHANICS:
 * - Format: Jumbled Sentence Puzzles with Multiple Choice Questions (MCQ) with 4 options each
 * - Total Questions: 4 diverse jumbled sentences covering literature, geography, science, and philosophy
 * - Hint System: Each question includes a helpful hint that can be revealed
 * - Timer Integration: Real-time countdown with global game timer
 * - Navigation Protection: Prevents accidental page refresh/navigation during quiz
 *
 * GAME FLOW:
 * 1. Question Selection: Questions are presented sequentially (no random order)
 * 2. Answer Selection: Players choose from 4 multiple-choice options (A, B, C, D)
 * 3. Hint Usage: Optional hints available for each question (affects scoring)
 * 4. Answer Submission: Submit selected answer or skip to next question
 * 5. Progress Tracking: Visual progress bar and question counter
 * 6. Level Completion: Automatic progression after all questions answered/skipped
 *
 * STATISTICS TRACKING:
 * - Correct Answers: Number of questions answered correctly
 * - Incorrect Answers: Number of questions answered incorrectly
 * - Skipped Questions: Number of questions skipped without answering
 * - Hints Used: Total number of hints revealed during the level
 * - Time Taken: Duration from level start to completion
 * - Consecutive Correct: Tracks streaks for bonus calculations
 *
 * SCORING ALGORITHM:
 * Base Points:
 * - Correct Answer (no hint): 1500 points
 * - Correct Answer (with hint): 1000 points
 * - Incorrect Answer: -400 points penalty
 * - Skipped Question: -750 points penalty
 *
 * Bonus Systems:
 * - Consecutive Correct Bonus: +200 points for every 3 consecutive correct answers
 * - Time Bonus (based on completion speed):
 *   * Under 1 min: +250 points
 *   * 1-1.5 min: +225 points
 *   * 1.5-2 min: +200 points
 *   * 2-2.5 min: +175 points
 *   * 2.5-3 min: +150 points
 *   * 3-3.5 min: +125 points
 *   * 3.5-4 min: +100 points
 *   * 4-4.5 min: +75 points
 *   * 4.5-5 min: +50 points
 *   * 5-5.5 min: +25 points
 *   * Over 5.5 min: No time bonus
 *
 * FINAL SCORE CALCULATION:
 * Total Score = (Base Points) + (Consecutive Bonus) + (Time Bonus)
 * Minimum Score: 0 (negative scores are clamped to zero)
 *
 * INTEGRATION FEATURES:
 * - Global Timer: Respects game-wide time limits and displays remaining time
 * - Team Management: Updates team statistics and progression status
 * - API Integration: Real-time updates to database for scores and statistics
 * - Navigation Control: Prevents data loss through page navigation protection
 * - Toast Notifications: User feedback for actions and errors
 *
 * LEVEL COMPLETION SUMMARY:
 * Upon completion, players receive detailed feedback including:
 * - Performance breakdown (correct/incorrect/skipped/hints)
 * - Time taken to complete the level
 * - Score breakdown with bonuses and penalties
 * - Performance rating based on accuracy and speed
 * - Navigation to next level (Level 24)
 */

"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import { Trophy, Timer, HelpCircle, SkipForward, ArrowRight, CheckCircle, Target } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { toast } from "sonner";
import { Team, getGameTimeRemaining, formatTimeRemaining, getGameTimerStatus } from "@/lib/supabase";

interface Question {
  id: number;
  question: string;
  options: string[];
  correct: string;
  hint: string;
}

/**
 * LEVEL-23 QUESTION BANK
 *
 * A collection of 4 jumbled sentence puzzles covering:
 * - Literature & Poetry (Edgar Allan Poe's "The Raven")
 * - Geography & World Knowledge (Sahara Desert facts)
 * - Science & Astronomy (Saturn and its moons)
 * - Philosophy & Human Nature (democracy and grammar)
 *
 * Each question includes:
 * - A jumbled sentence that needs to be unscrambled
 * - 4 carefully crafted options with plausible alternatives
 * - One correct unscrambled answer
 * - A helpful hint that provides context without giving away the answer
 */
const questions: Question[] = [
  {
    id: 1,
    question: "Unscramble this jumbled sentence: 'Edgar Alnla Poe worte 'Teh Rvaen' to scraes teh peolp at nhgit, wihle teh wolfs ulnig ot eht mnonr, csreahd eht slacne.'",
    options: [
      "Edgar Allan Poe wrote 'The Raven' to scare the people at night, while the wolves howling at the moon, crashed the silence.",
      "Edgar Allan Poe wrote 'The Raven' to please the people at night, while the wolves running to the moon, broke the silence.",
      "Edgar Allan Poe created 'The Raven' to frighten the people at night, while the wolves crying at the moon, shattered the silence.",
      "Edgar Allan Poe composed 'The Raven' to terrify the people at night, while the wolves singing to the moon, disturbed the silence."
    ],
    correct: "Edgar Allan Poe wrote 'The Raven' to scare the people at night, while the wolves howling at the moon, crashed the silence.",
    hint: "Focus on the famous American poet and his most well-known poem about a dark bird, and think about what wolves do at night."
  },
  {
    id: 2,
    question: "Unscramble this jumbled sentence: 'Evrneyoen knwos taht hte lrigseet dsert in hte wrold is eht ahcbl Dsesert, sppraed on 9.2 mln qkaumre.'",
    options: [
      "Everyone knows that the largest desert in the world is the Sahara Desert, spread on 9.2 million square km.",
      "Everyone knows that the biggest desert in the world is the Gobi Desert, spread on 9.2 million square km.",
      "Everyone knows that the greatest desert in the world is the Kalahari Desert, spread on 9.2 million square km.",
      "Everyone knows that the widest desert in the world is the Arabian Desert, spread on 9.2 million square km."
    ],
    correct: "Everyone knows that the largest desert in the world is the Sahara Desert, spread on 9.2 million square km.",
    hint: "Think about the most famous desert in Africa, known for its vast size and sandy dunes."
  },
  {
    id: 3,
    question: "Unscramble this jumbled sentence: 'A slyciendf, wlhiie a gnihtarc teh rceaat of ytinutar, dsevoeicrd teh rpidyc fo eht lciham.'",
    options: [
      "A scientist, while charting the crater of Saturn, discovered the orbit of the moons.",
      "A scientist, while studying the crater of Jupiter, discovered the orbit of the planets.",
      "A scientist, while mapping the crater of Mars, discovered the orbit of the asteroids.",
      "A scientist, while exploring the crater of Venus, discovered the orbit of the comets."
    ],
    correct: "A scientist, while charting the crater of Saturn, discovered the orbit of the moons.",
    hint: "Think about the ringed planet in our solar system and what celestial bodies orbit around it."
  },
  {
    id: 4,
    question: "Unscramble this jumbled sentence: 'A phsloiphre whti a lcookaed ppie, sniurttg na rwod, dsicovered eht tdyoemrca of humnas, whtiout tcuagh a sinlgel grmaer ruel.'",
    options: [
      "A philosopher with a crooked pipe, stirring a word, discovered the democracy of humans, without touching a single grammar rule.",
      "A philosopher with a broken pipe, mixing a word, discovered the freedom of humans, without learning a single grammar rule.",
      "A philosopher with a curved pipe, changing a word, discovered the equality of humans, without knowing a single grammar rule.",
      "A philosopher with a bent pipe, creating a word, discovered the rights of humans, without studying a single grammar rule."
    ],
    correct: "A philosopher with a crooked pipe, stirring a word, discovered the democracy of humans, without touching a single grammar rule.",
    hint: "Think about a deep thinker who might smoke a pipe and contemplate the nature of human governance and language."
  }
];

export default function Level23Page() {
  const [team, setTeam] = useState<Team | null>(null);
  const [initialTeamStats, setInitialTeamStats] = useState<{
    correct_questions: number;
    incorrect_questions: number;
    skipped_questions: number;
    hint_count: number;
  } | null>(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState<string>("");
  const [showHint, setShowHint] = useState(false);
  const [levelStartTime] = useState<Date>(new Date());
  const [timeRemaining, setTimeRemaining] = useState<number>(0);
  const [timerStatus, setTimerStatus] = useState<'not_started' | 'active' | 'expired'>('not_started');
  const [loading, setLoading] = useState(true);
  const [isCompleted, setIsCompleted] = useState(false);
  const [completionTimeMinutes, setCompletionTimeMinutes] = useState<number>(0);
  const [levelStats, setLevelStats] = useState({
    correct: 0,
    incorrect: 0,
    skipped: 0,
    hintsUsed: 0
  });
  const router = useRouter();

  const fetchTeamData = useCallback(async (teamCode: string) => {
    try {
      const response = await fetch(`/api/teams/${teamCode}`);
      if (!response.ok) {
        throw new Error('Failed to fetch team data');
      }
      const teamData = await response.json();
      setTeam(teamData);

      // Store initial team statistics to track level-specific changes
      setInitialTeamStats({
        correct_questions: teamData.correct_questions,
        incorrect_questions: teamData.incorrect_questions,
        skipped_questions: teamData.skipped_questions,
        hint_count: teamData.hint_count
      });

      if (teamData.current_level > 23) {
        toast.info("You've already completed this level!");
        router.push('/levels');
        return;
      }

      if (teamData.current_level < 23) {
        toast.error("You need to complete previous levels first!");
        router.push('/levels');
        return;
      }

      setLoading(false);
    } catch (error) {
      console.error('Error fetching team data:', error);
      toast.error("Failed to load team data");
      router.push('/');
    }
  }, [router]);

  useEffect(() => {
    const teamCode = localStorage.getItem('team_code');
    if (!teamCode) {
      toast.error("No team found. Please register first.");
      router.push('/');
      return;
    }

    fetchTeamData(teamCode);

    // Prevent navigation during quiz
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      e.preventDefault();
      e.returnValue = '';
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [fetchTeamData, router]);

  useEffect(() => {
    if (team) {
      const timer = setInterval(() => {
        const remaining = getGameTimeRemaining(team);
        const status = getGameTimerStatus(team);

        setTimeRemaining(remaining);
        setTimerStatus(status);

        if (status === 'expired' && timerStatus !== 'expired') {
          toast.error("Time's up! The game has ended.");
        }
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [team, timerStatus]);

  const getTimerDisplay = (): { text: string; className: string } => {
    switch (timerStatus) {
      case 'not_started':
        return { text: 'Game Not Started', className: 'text-gray-500' };
      case 'expired':
        return { text: '00:00:00', className: 'text-red-600' };
      case 'active':
        return { text: formatTimeRemaining(timeRemaining), className: 'text-red-600' };
      default:
        return { text: 'Game Not Started', className: 'text-gray-500' };
    }
  };

  const updateTeamStats = async (stats: Record<string, number>) => {
    const teamCode = localStorage.getItem('team_code');
    if (!teamCode) return;

    try {
      await fetch(`/api/teams/${teamCode}/stats`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(stats)
      });
    } catch (error) {
      console.error('Error updating stats:', error);
    }
  };

  const handleHint = () => {
    if (!showHint) {
      setShowHint(true);
      const newStats = { ...levelStats };
      newStats.hintsUsed++;
      setLevelStats(newStats);
    }
  };
